#!/usr/bin/env python3
"""
投屏功能启动脚本
"""

import sys
import os

# 添加模块路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

def main():
    try:
        from PyQt5.QtWidgets import QApplication
        from screen_dlan_mod import ScreenDLA<PERSON>
        
        print("启动投屏功能...")
        
        # 创建QApplication
        app = QApplication(sys.argv)
        app.setQuitOnLastWindowClosed(False)
        
        # 创建投屏窗口
        screen_dlan = ScreenDLAN()
        screen_dlan.show()
        
        print("投屏界面已打开！")
        print("请在界面中:")
        print("1. 等待设备搜索完成")
        print("2. 选择您的安卓电视设备")
        print("3. 点击'投屏'按钮开始投屏")
        
        # 运行应用
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
