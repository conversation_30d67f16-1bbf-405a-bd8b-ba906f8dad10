import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import Qt
from ui.sys_css import MyCss
import subprocess  

class PptControl(QMainWindow):
    def __init__(self):
        super().__init__()
        self.screen_height = QDesktopWidget().screenGeometry().height()
        self.screen_width = QDesktopWidget().screenGeometry().width()
        self.setWindowTitle("PPT 控制器")
        # self.setGeometry(200, 200, 200, 64)  # 窗口初始大小
        self.setGeometry((self.screen_width - 350) , (self.screen_height)-140, 250, 64)
        self.setWindowFlags(Qt.FramelessWindowHint |Qt.WindowStaysOnTopHint)  # 设置为无边框和置顶

        # 创建主Widget
        self.central_widget = QWidget(self)
        self.setCentralWidget(self.central_widget)
        self.central_widget.setStyleSheet(MyCss.mainBgcolora)

        # 创建布局
        layout = QHBoxLayout(self.central_widget)

        # 添加按钮
        start_button = QPushButton("开始放映", self)
        start_button.setFixedSize(64, 64)  # 设置按钮大小
        start_button.clicked.connect(self.start_ppt)
        layout.addWidget(start_button)

        prev_button = QPushButton("上一页", self)
        prev_button.setFixedSize(64, 64)  # 设置按钮大小
        prev_button.clicked.connect(self.prev_slide)
        layout.addWidget(prev_button)

        next_button = QPushButton("下一页", self)
        next_button.setFixedSize(64, 64)  # 设置按钮大小
        next_button.clicked.connect(self.next_slide)
        layout.addWidget(next_button)

        stop_button = QPushButton("结束放映", self)  # 新增按钮
        stop_button.setFixedSize(64, 64)  # 设置按钮大小
        stop_button.clicked.connect(self.stop_ppt)
        layout.addWidget(stop_button)
        # 添加退出按钮
        exit_button = QPushButton("退出", self)
        exit_button.setFixedSize(64, 64)  # 设置按钮大小
        exit_button.clicked.connect(self.exit_app)
        layout.addWidget(exit_button)

        # 使菜单可拖动
        self.old_pos = None

    def mousePressEvent(self, event):
        """记录鼠标按下位置"""
        if event.button() == Qt.LeftButton:
            self.old_pos = event.pos()

    def mouseMoveEvent(self, event):
        """根据鼠标移动更新窗口位置"""
        if self.old_pos is not None:
            diff = event.pos() - self.old_pos
            new_pos = self.pos() + diff
            self.move(new_pos)

    def mouseReleaseEvent(self, event):
        """释放鼠标时重置旧位置"""
        if event.button() == Qt.LeftButton:
            self.old_pos = None

    
    def activate_ppt_window(self):
        """在 Linux 上激活 PPT 窗口（兼容 WPS 或 LibreOffice）"""
        try:
            # 查找 PPT 窗口（假设标题包含 "WPS Presentation" 或 "LibreOffice Impress"）
            import subprocess
            result = subprocess.run(
                ["wmctrl", "-l"], 
                capture_output=True, 
                text=True
            )
            windows = result.stdout.splitlines()
            ppt_windows = [w for w in windows if "WPS Presentation" in w or "LibreOffice Impress" in w]

            if ppt_windows:
                window_id = ppt_windows[0].split()[0]  # 提取窗口 ID
                subprocess.run(["wmctrl", "-i", "-a", window_id])  # 激活窗口
                subprocess.run(["xdotool", "windowfocus", window_id])  # 确保焦点
            else:
                QMessageBox.warning(self, "警告", "未检测到 PPT 窗口")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"激活窗口失败: {str(e)}")
    
    def start_ppt(self):
        """开始放映 PPT（Linux 兼容）"""
        self.activate_ppt_window()
        subprocess.run(["xdotool", "key", "F5"])  # 代替 pyautogui.press('f5')

    def prev_slide(self):
        """上一页（Linux 兼容）"""
        self.activate_ppt_window()
        subprocess.run(["xdotool", "key", "Left"])  # 代替 pyautogui.press('left')

    def next_slide(self):
        """下一页（Linux 兼容）"""
        self.activate_ppt_window()
        subprocess.run(["xdotool", "key", "Right"])  # 代替 pyautogui.press('right')

    def stop_ppt(self):
        """结束放映（Linux 兼容）"""
        self.activate_ppt_window()
        subprocess.run(["xdotool", "key", "Escape"])  # 代替 pyautogui.press('esc')

    def exit_app(self):
        """退出应用"""
        self.close()  # 关闭主窗口


if __name__ == "__main__":
    app = QApplication(sys.argv)
    mainWin = PptControl()
    mainWin.show()
    sys.exit(app.exec_())
