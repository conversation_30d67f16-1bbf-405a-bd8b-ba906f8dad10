from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtCore import QUrl, Qt
import json
import sys
import os

class Groupscreen(QMainWindow):
    def __init__(self):
        super().__init__()
        
        self.setWindowTitle("多屏互动")
        self.setGeometry(600, 200, 800, 600)  # 设置窗口大小
        self.setWindowFlags(Qt.Tool | self.windowFlags())

        # 创建中心部件和布局
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.layout = QVBoxLayout(self.central_widget)

        # 创建 QWebEngineView 用于显示 HTML 内容
        self.webview = QWebEngineView()
        self.layout.addWidget(self.webview)
       
        # 加载视频 URL 并生成 HTML
        video_urls =[
        "http://192.168.0.11:8888/stream01/",  
        "http://192.168.0.11:8888/stream02/",  
        "http://192.168.0.11:8888/stream03/",  
        "http://192.168.0.11:8888/stream04/",  
        "http://192.168.0.11:8888/stream05/",  
        "http://192.168.0.11:8888/stream06/",  
        "http://192.168.0.11:8888/stream07/",  
        "http://192.168.0.11:8888/stream08/",  
        "http://192.168.0.11:8888/stream09/" 
        ]
        if not video_urls:
            print("未找到有效的 URL！")
            self.webview.setHtml("<h1>未找到有效的 URL！</h1>")  # 显示错误信息
        else:
            # 根据 URL 列表生成 HTML 内容，并加载到 QWebEngineView 中
            # html_content = self._generate_html(video_urls)
            html_content = self._generate_html_mjpeg(video_urls) 
            # self.webview.setHtml(html_content)
            base_url = QUrl(f"http://{video_urls[0].split('/')[2]}/") 
            self.webview.setHtml(html_content, baseUrl=base_url)
            
            

    def _generate_html(self, video_urls):
        """生成包含多屏布局的 HTML 内容"""
        html_content = f"""  
        <!DOCTYPE html>  
        <html>  
        <head>  
            <title>分屏布局</title>  
            <style>  
                body, html {{
                    margin: 0;  
                    padding: 0;  
                    height: 100vh;  
                    width: 100vw;
                    overflow: hidden; /* 防止出现滚动条 */
                    background-color: #333;
                }}  
                .container {{  
                    display: grid;  
                    width: 100%;  
                    height: 100%;  
                    gap: 3px;
                }}  
                .stream {{  
                    background-color: #000;
                    overflow: hidden;  
                    display: none;  
                }}  
                iframe {{  
                    width: 100%;  
                    height: 100%;  
                    border: none;  
                }}  
                .controls {{  
                    position: fixed;  
                    bottom: 10px;  
                    left: 50%;  
                    transform: translateX(-50%);  
                    background-color: rgba(0, 0, 0, 0.5);
                    padding: 5px;
                    border-radius: 5px;
                    z-index: 100;
                }}  
                .controls button {{  
                    padding: 8px 16px;  
                    margin: 0 5px;  
                    font-size: 14px;  
                    background-color: #007bff;  
                    color: #fff;  
                    border: none;  
                    border-radius: 4px;  
                    cursor: pointer;  
                }}
                .controls button:hover {{
                    background-color: #0056b3;
                }}
            </style>  
        </head>  
        <body>  
            <div class="container">  
        """

        # 添加视频流 iframe
        for url in video_urls:
            html_content += f"""  
                <div class="stream">  
                    <iframe src="{url}" allowfullscreen="1" allow="autoplay; encrypted-media"></iframe>  
                </div>  
            """

        html_content += """  
            </div>  
            
            <div class="controls">  
                <button onclick="setLayout(1)">1分屏</button>  
                <button onclick="setLayout(4)">4分屏</button>  
                <button onclick="setLayout(6)">6分屏</button>  
                <button onclick="setLayout(9)">9分屏</button>  
            </div>  

            <script>  
                const container = document.querySelector('.container');  
                const streams = document.querySelectorAll('.stream');  

                function setLayout(layout) {  
                    streams.forEach((stream, index) => {  
                        stream.style.display = (index < layout) ? 'block' : 'none';
                        // 重置 gridArea，防止旧布局影响新布局
                        stream.style.gridArea = 'auto';
                    });  

                    switch (layout) {  
                        case 1:  
                            container.style.gridTemplateColumns = '1fr';  
                            container.style.gridTemplateRows = '1fr';  
                            break;
                        case 4:  
                            container.style.gridTemplateColumns = '1fr 1fr';  
                            container.style.gridTemplateRows = '1fr 1fr';  
                            break;  
                        case 6: // 修正后的6分屏布局 (一个大屏在左，四个小屏在右)
                            container.style.gridTemplateColumns = '2fr 1fr 1fr';  
                            container.style.gridTemplateRows = '1fr 1fr';  
                            streams[0].style.gridArea = '1 / 1 / 3 / 2'; // 第一个流占2行1列
                            streams[1].style.gridArea = '1 / 2 / 2 / 3';
                            streams[2].style.gridArea = '1 / 3 / 2 / 4';
                            streams[3].style.gridArea = '2 / 2 / 3 / 3';
                            streams[4].style.gridArea = '2 / 3 / 3 / 4';
                            // 第6个流在这个布局下不显示，只显示5个。如果要6个，可以这样：
                            // container.style.gridTemplateColumns = '1fr 1fr 1fr';
                            // container.style.gridTemplateRows = '1fr 1fr';
                            break;
                        case 9:  
                            container.style.gridTemplateColumns = '1fr 1fr 1fr';  
                            container.style.gridTemplateRows = '1fr 1fr 1fr';  
                            break;  
                        default:  
                            break;  
                    }  
                }  

                // 默认显示 9 分屏
                setLayout(9);
            </script>  
        </body>  
        </html>  
        """
        return html_content

    def _generate_html_mjpeg(self, video_urls):
        """生成包含多屏布局的 HTML 内容，使用 MJPEG 流 (最终修正版)"""
        
        mjpeg_urls = [url.strip('/') + '.mjpg' for url in video_urls]
        
        # 注意 f-string 中 { 和 } 的转义：单个 { 写成 {{, 单个 } 写成 }}
        html_content = f"""  
        <!DOCTYPE html>  
        <html>  
        <head>  
            <title>分屏布局 (MJPEG)</title>  
            <style>  
                body, html {{
                    margin: 0;  
                    padding: 0;  
                    height: 100vh;  
                    width: 100vw;
                    overflow: hidden;
                    background-color: #333;
                }}  
                .container {{  
                    display: grid;  
                    width: 100%;  
                    height: 100%;  
                    gap: 3px;
                }}  
                .stream {{  
                    background-color: #000;
                    overflow: hidden;  
                    display: none;
                    justify-content: center;
                    align-items: center;
                }}  
                img {{  
                    width: 100%;  
                    height: 100%;  
                    object-fit: contain;
                    /* 如果图片加载失败，显示一个提示 */
                    color: white;
                    text-align: center;
                }}  
                .controls {{  
                    position: fixed;  
                    bottom: 10px;  
                    left: 50%;  
                    transform: translateX(-50%);  
                    background-color: rgba(0, 0, 0, 0.5);
                    padding: 5px;
                    border-radius: 5px;
                    z-index: 100;
                }}  
                .controls button {{  
                    padding: 8px 16px;  
                    margin: 0 5px;  
                    font-size: 14px;  
                    background-color: #007bff;  
                    color: #fff;  
                    border: none;  
                    border-radius: 4px;  
                    cursor: pointer;  
                }}
                .controls button:hover {{
                    background-color: #0056b3;
                }}
            </style>  
        </head>  
        <body>  
            <div class="container">  
        """
        for url in mjpeg_urls:
            html_content += f"""  
                <div class="stream">  
                    <img src="{url}" alt="Video Stream {url}" />
                </div>  
            """
        # JS部分也需要转义 { 和 }
        html_content += f"""  
            </div>  
            <div class="controls">  
                <button onclick="setLayout(1)">1分屏</button>  
                <button onclick="setLayout(4)">4分屏</button>  
                <button onclick="setLayout(6)">6分屏</button>  
                <button onclick="setLayout(9)">9分屏</button>  
            </div>  
            <script>  
                const container = document.querySelector('.container');  
                const streams = document.querySelectorAll('.stream');  
                function setLayout(layout) {{  
                    streams.forEach((stream, index) => {{  
                        stream.style.display = (index < layout) ? 'flex' : 'none';
                        stream.style.gridArea = 'auto';
                    }});  
                    switch (layout) {{  
                        case 1:  
                            container.style.gridTemplateColumns = '1fr';  
                            container.style.gridTemplateRows = '1fr';  
                            break;
                        case 4:  
                            container.style.gridTemplateColumns = '1fr 1fr';  
                            container.style.gridTemplateRows = '1fr 1fr';  
                            break;  
                        case 6:
                            container.style.gridTemplateColumns = 'repeat(3, 1fr)';
                            container.style.gridTemplateRows = 'repeat(2, 1fr)';
                            break;
                        case 9:  
                            container.style.gridTemplateColumns = '1fr 1fr 1fr';  
                            container.style.gridTemplateRows = '1fr 1fr 1fr';  
                            break;  
                        default:  
                            break;  
                    }}  
                }}  
                setLayout(9);
            </script>  
        </body>  
        </html>  
        """
        return html_content

if __name__ == "__main__":
    import os
    # 设置环境变量以启用远程调试
    os.environ['QTWEBENGINE_REMOTE_DEBUGGING'] = '8090' # 可以在 8090 端口调试

    app = QApplication(sys.argv)
    groupscreen = Groupscreen()
    groupscreen.show()

    print("WebEngine 远程调试已开启，请在浏览器中访问 http://localhost:8090")
    
    sys.exit(app.exec_())
