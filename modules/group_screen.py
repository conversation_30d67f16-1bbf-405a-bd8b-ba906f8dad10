from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtCore import QUrl, Qt
import json
import sys
import os

class Groupscreen(QMainWindow):
    def __init__(self):
        super().__init__()
        
        self.setWindowTitle("多屏互动")
        self.setGeometry(600, 200, 800, 600)  # 设置窗口大小
        self.setWindowFlags(Qt.Tool | self.windowFlags())

        # 创建中心部件和布局
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.layout = QVBoxLayout(self.central_widget)

        # 创建 QWebEngineView 用于显示 HTML 内容
        self.webview = QWebEngineView()
        self.layout.addWidget(self.webview)
       
        # 加载视频 URL 并生成 HTML
        video_urls = [
        "http://192.168.0.11:8888/stream01/",  
        "http://192.168.0.11:8888/stream02/",  
        "http://192.168.0.11:8888/stream03/",  
        "http://192.168.0.11:8888/stream04/",  
        "http://192.168.0.11:8888/stream05/",  
        "http://192.168.0.11:8888/stream06/",  
        "http://192.168.0.11:8888/stream07/",  
        "http://192.168.0.11:8888/stream08/",  
        "http://192.168.0.11:8888/stream09/" 
        ]
        if not video_urls:
            print("未找到有效的 URL！")
            self.webview.setHtml("<h1>未找到有效的 URL！</h1>")  # 显示错误信息
        else:
            # 根据 URL 列表生成 HTML 内容，并加载到 QWebEngineView 中
            html_content = self._generate_html(video_urls)
            self.webview.setHtml(html_content)

    def _load_video_urls(self, json_file):
        """加载视频 URL"""
        try:
            with open(json_file, 'r', encoding='utf-8') as file:
                data = json.load(file)
                return data.get("video_urls", [])
        except json.JSONDecodeError:
            print(f"Error: The file '{json_file}' contains invalid JSON.")
            return []
        except FileNotFoundError:
            print(f"Error: The file '{json_file}' does not exist.")
            return []
        except Exception as e:
            print(f"An unexpected error occurred: {e}")
            return []

    def _generate_html(self, video_urls):
        """生成包含多屏布局的 HTML 内容"""
        html_content = f"""  
        <!DOCTYPE html>  
        <html>  
        <head>  
            <title>分屏布局</title>  
            <style>  
                body {{  
                    margin: 0;  
                    padding: 0;  
                    display: flex;  
                    justify-content: center;  
                    align-items: center;  
                    height: 100vh;  
                    background-color: #f0f0f0;  
                }}  
                .container {{  
                    display: grid;  
                    width: 100%;  
                    height: 100%;  
                    gap: 3px; /* Gap between grid items for visual separation */  
                }}  
                .stream {{  
                    border-radius: 6px;  
                    background-color: #000; /* Background color for visual separation */  
                    overflow: hidden;  
                    display: none;  
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);  
                }}  
                iframe {{  
                    width: 100%;  
                    height: 100%;  
                    border: none;  
                }}  
                .controls {{  
                    position: fixed;  
                    bottom: 10px;  
                    left: 50%;  
                    transform: translateX(-50%);  
                    text-align: center;  
                }}  
                .controls button {{  
                    padding: 8px 16px;  
                    margin: 0 5px;  
                    font-size: 14px;  
                    background-color: #007bff;  
                    color: #fff;  
                    border: none;  
                    border-radius: 4px;  
                    cursor: pointer;  
                }}  
            </style>  
        </head>  
        <body>  
            <div class="container">  
        """

        # 添加视频流 iframe
        for url in video_urls:
            html_content += f"""  
                <div class="stream">  
                    <iframe src="{url}" allowfullscreen="1" allow="autoplay; encrypted-media"></iframe>  
                </div>  
            """

        html_content += """  
            </div>  
            
            <div class="controls">  
                <button onclick="setLayout(1)">1分屏</button>  
                <button onclick="setLayout(2)">2分屏</button>  
                <button onclick="setLayout(3)">3分屏</button>  
                <button onclick="setLayout(4)">4分屏</button>  
                <button onclick="setLayout(6)">6分屏</button>  
                <button onclick="setLayout(9)">9分屏</button>  
            </div>  

            <script>  
                const container = document.querySelector('.container');  
                const streams = document.querySelectorAll('.stream');  

                function setLayout(layout) {  
                    streams.forEach((stream, index) => {  
                        stream.style.display = (index < layout) ? 'block' : 'none';  
                    });  

                    switch (layout) {  
                        case 1:  
                            container.style.gridTemplateColumns = '1fr';  
                            container.style.gridTemplateRows = '1fr';  
                            streams[0].style.gridArea = '1 / 1 / 2 / 2';  
                            break;  
                        case 2:  
                            container.style.gridTemplateColumns = '1fr 1fr';  
                            container.style.gridTemplateRows = '1fr';  
                            streams[0].style.gridArea = '1 / 1 / 2 / 2';  
                            streams[1].style.gridArea = '1 / 2 / 2 / 3';  
                            break;  
                        case 3:  
                            container.style.gridTemplateColumns = '2fr 1fr';  
                            container.style.gridTemplateRows = '1fr 1fr';  
                            streams[0].style.gridArea = '1 / 1 / 3 / 2';  
                            streams[1].style.gridArea = '1 / 2 / 2 / 3';  
                            streams[2].style.gridArea = '2 / 2 / 3 / 3';  
                            break;  
                        case 4:  
                            container.style.gridTemplateColumns = '1fr 1fr';  
                            container.style.gridTemplateRows = '1fr 1fr';  
                            streams[0].style.gridArea = '1 / 1 / 2 / 2';  
                            streams[1].style.gridArea = '1 / 2 / 2 / 3';  
                            streams[2].style.gridArea = '2 / 1 / 3 / 2';  
                            streams[3].style.gridArea = '2 / 2 / 3 / 3';  
                            break;  
                        case 6:  
                            container.style.gridTemplateColumns = '1fr 1fr 1fr';  
                            container.style.gridTemplateRows = '1fr 1fr 1fr';  
                            streams[0].style.gridArea = '1 / 1 / 3 / 3';  
                            streams[1].style.gridArea = '1 / 3 / 1 / 3';  
                            streams[2].style.gridArea = '2 / 3 / 2 / 3';  
                            streams[3].style.gridArea = '3 / 1 / 3 / 1';  
                            streams[4].style.gridArea = '3 / 2 / 3 / 2';  
                            streams[5].style.gridArea = '3 / 3 / 3 / 3';  
                            break;  
                        case 9:  
                            container.style.gridTemplateColumns = '1fr 1fr 1fr';  
                            container.style.gridTemplateRows = '1fr 1fr 1fr';  
                            streams[0].style.gridArea = '1 / 1 / 2 / 2';  
                            streams[1].style.gridArea = '1 / 2 / 2 / 3';  
                            streams[2].style.gridArea = '1 / 3 / 2 / 4';  
                            streams[3].style.gridArea = '2 / 1 / 3 / 2';  
                            streams[4].style.gridArea = '2 / 2 / 3 / 3';  
                            streams[5].style.gridArea = '2 / 3 / 3 / 4';  
                            streams[6].style.gridArea = '3 / 1 / 4 / 2';  
                            streams[7].style.gridArea = '3 / 2 / 4 / 3';  
                            streams[8].style.gridArea = '3 / 3 / 4 / 4';  
                            break;  
                        default:  
                            break;  
                    }  
                }  

                setLayout(4); // 默认显示 4 分屏  
            </script>  
        </body>  
        </html>  
        """
        return html_content
# 测试代码
if __name__ == "__main__":
 
    app = QApplication(sys.argv)
    groupscreen = Groupscreen()
    groupscreen.show()
    sys.exit(app.exec_())
