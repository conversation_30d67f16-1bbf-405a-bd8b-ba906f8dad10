import sys
import os
import threading
import http.server
import socketserver
import subprocess
import signal
import time
from collections import defaultdict

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtCore import QUrl, Qt

# --- 全局变量，用于在主窗口和HTTP服务器间共享数据 ---
HTML_CONTENT_TO_SERVE = "<h1>Initializing...</h1>"

# 全局进程管理器，用于跟踪所有的 FFmpeg 进程
FFMPEG_PROCESSES = {}
PROCESS_LOCK = threading.Lock()

# HTTP请求处理器
class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """此处理器仅用于返回全局变量中的HTML内容"""
    def do_GET(self):
        if self.path == '/':
            self.send_response(200)
            self.send_header("Content-type", "text/html; charset=utf-8")
            self.end_headers()
            # 从全局变量获取HTML并编码为UTF-8字节流
            self.wfile.write(HTML_CONTENT_TO_SERVE.encode('utf-8'))
        elif self.path.startswith("/mjpeg_proxy/"):
            # MJPEG 代理端点
            stream_name = self.path.replace("/mjpeg_proxy/", "").strip("/")
            self.proxy_mjpeg_stream(stream_name)
        else:
            # 对于其他任何请求，返回404
            self.send_error(404, "File Not Found: %s" % self.path)

    def proxy_mjpeg_stream(self, stream_name):
        """代理 MJPEG 流"""
        global FFMPEG_PROCESSES, PROCESS_LOCK

        process = None
        try:
            print(f"开始代理流: {stream_name}")

            # 构建 MediaMTX RTSP 到 MJPEG 的转换 URL
            rtsp_url = f"rtsp://************:8554/{stream_name}"

            # 设置 MJPEG 响应头
            self.send_response(200)
            self.send_header('Content-Type', 'multipart/x-mixed-replace; boundary=frame')
            self.send_header('Cache-Control', 'no-cache')
            self.send_header('Connection', 'close')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()

            # 检查是否已有该流的进程在运行
            with PROCESS_LOCK:
                if stream_name in FFMPEG_PROCESSES:
                    old_process = FFMPEG_PROCESSES[stream_name]
                    if old_process.poll() is None:  # 进程仍在运行
                        try:
                            old_process.terminate()
                            old_process.wait(timeout=2)
                        except:
                            try:
                                old_process.kill()
                            except:
                                pass
                    del FFMPEG_PROCESSES[stream_name]

            # 启动新的 FFmpeg 进程
            ffmpeg_cmd = [
                'ffmpeg',
                '-rtsp_transport', 'tcp',  # 使用 TCP 传输，更稳定
                '-i', rtsp_url,
                '-c:v', 'mjpeg',
                '-q:v', '8',  # 稍微降低质量以提高性能
                '-f', 'mjpeg',
                '-r', '15',  # 15 FPS
                '-reconnect', '1',  # 自动重连
                '-reconnect_streamed', '1',
                '-reconnect_delay_max', '2',
                'pipe:1'
            ]

            print(f"启动 FFmpeg 进程: {' '.join(ffmpeg_cmd)}")
            process = subprocess.Popen(
                ffmpeg_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                bufsize=0  # 无缓冲
            )

            # 将进程添加到全局管理器
            with PROCESS_LOCK:
                FFMPEG_PROCESSES[stream_name] = process

            # 读取 FFmpeg 输出并发送 MJPEG 帧
            frame_data = b''
            frame_count = 0

            while True:
                # 检查进程是否仍在运行
                if process.poll() is not None:
                    print(f"FFmpeg 进程已退出: {stream_name}")
                    break

                try:
                    chunk = process.stdout.read(4096)  # 增加缓冲区大小
                    if not chunk:
                        print(f"没有更多数据: {stream_name}")
                        break

                    frame_data += chunk

                    # 查找 JPEG 帧边界
                    while True:
                        start = frame_data.find(b'\xff\xd8')  # JPEG 开始标记
                        end = frame_data.find(b'\xff\xd9')    # JPEG 结束标记

                        if start != -1 and end != -1 and end > start:
                            # 找到完整的 JPEG 帧
                            jpeg_frame = frame_data[start:end+2]
                            frame_data = frame_data[end+2:]

                            # 发送 MJPEG 帧
                            try:
                                self.wfile.write(b'--frame\r\n')
                                self.wfile.write(b'Content-Type: image/jpeg\r\n')
                                self.wfile.write(f'Content-Length: {len(jpeg_frame)}\r\n\r\n'.encode())
                                self.wfile.write(jpeg_frame)
                                self.wfile.write(b'\r\n')
                                self.wfile.flush()

                                frame_count += 1
                                if frame_count % 50 == 0:  # 每50帧打印一次
                                    print(f"已发送 {frame_count} 帧: {stream_name}")

                            except (BrokenPipeError, ConnectionResetError):
                                print(f"客户端断开连接: {stream_name}")
                                break
                        else:
                            break

                except Exception as e:
                    print(f"读取数据时出错 {stream_name}: {e}")
                    break

        except Exception as e:
            print(f"MJPEG 代理错误 {stream_name}: {e}")
        finally:
            # 清理进程
            if process:
                try:
                    if process.poll() is None:
                        process.terminate()
                        process.wait(timeout=2)
                except:
                    try:
                        if process.poll() is None:
                            process.kill()
                    except:
                        pass

                # 从全局管理器中移除
                with PROCESS_LOCK:
                    if stream_name in FFMPEG_PROCESSES:
                        del FFMPEG_PROCESSES[stream_name]

            print(f"代理流结束: {stream_name}")

def cleanup_all_ffmpeg_processes():
    """清理所有 FFmpeg 进程"""
    global FFMPEG_PROCESSES, PROCESS_LOCK

    print("正在清理所有 FFmpeg 进程...")
    with PROCESS_LOCK:
        for stream_name, process in list(FFMPEG_PROCESSES.items()):
            try:
                if process.poll() is None:
                    print(f"终止进程: {stream_name}")
                    process.terminate()
                    try:
                        process.wait(timeout=3)
                    except subprocess.TimeoutExpired:
                        print(f"强制杀死进程: {stream_name}")
                        process.kill()
                        process.wait()
            except Exception as e:
                print(f"清理进程时出错 {stream_name}: {e}")

        FFMPEG_PROCESSES.clear()
    print("所有 FFmpeg 进程已清理完成")

class Groupscreen(QMainWindow):
    def __init__(self):
        super().__init__()
        
        self.httpd = None # 用于持有HTTP服务器实例
        self.http_thread = None # 用于持有服务器线程
        
        self.setWindowTitle("多屏互动")
        self.setGeometry(600, 200, 800, 600)
        self.setWindowFlags(Qt.Tool | self.windowFlags())

        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.layout = QVBoxLayout(self.central_widget)
        self.layout.setContentsMargins(0, 0, 0, 0) # 布局铺满窗口

        self.webview = QWebEngineView()
        self.layout.addWidget(self.webview)
       
        # 1. 准备视频流 URL
        video_urls =[
            "http://************:8888/stream01/",  
            "http://************:8888/stream02/",  
            "http://************:8888/stream03/",  
            "http://************:8888/stream04/",  
            "http://************:8888/stream05/",  
            "http://************:8888/stream06/",  
            "http://************:8888/stream07/",  
            "http://************:8888/stream08/",  
            "http://************:8888/stream09/" 
        ]
        
        # 2. 生成HTML内容，并存入全局变量
        global HTML_CONTENT_TO_SERVE
        HTML_CONTENT_TO_SERVE = self._generate_html_mjpeg_proxy(video_urls)
        
        # 3. 启动本地HTTP服务器
        self.start_local_server()
        
        # 4. 让 QWebEngineView 加载本地服务器的地址
        if self.httpd:
            port = self.httpd.server_address[1]
            self.webview.load(QUrl(f"http://localhost:{port}"))

    def start_local_server(self):
        """在一个新线程中启动本地HTTP服务器"""
        try:
            # 端口号设为0，让操作系统自动选择一个可用的端口
            self.httpd = socketserver.TCPServer(("", 0), MyHTTPRequestHandler)
            port = self.httpd.server_address[1]
            print(f"本地迷你Web服务器已在 http://localhost:{port} 启动")
            
            # 创建并启动一个后台线程来运行服务器，这样它就不会阻塞GUI
            self.http_thread = threading.Thread(target=self.httpd.serve_forever)
            self.http_thread.daemon = True  # 设置为守护线程，主程序退出时它会自动退出
            self.http_thread.start()
        except Exception as e:
            print(f"启动本地服务器失败: {e}")
            self.httpd = None

    def _generate_html_mjpeg_proxy(self, video_urls):
        """生成包含多屏布局的 HTML 内容，使用 MJPEG 代理"""
        # 从原始 URL 中提取流名称，并构建代理 URL
        mjpeg_proxy_urls = []
        for url in video_urls:
            # 从 http://************:8888/stream01/ 提取 stream01
            stream_name = url.strip('/').split('/')[-1]
            proxy_url = f"/mjpeg_proxy/{stream_name}"
            mjpeg_proxy_urls.append(proxy_url)

        # 创建 img 标签部分
        streams_html = ""
        for i, url in enumerate(mjpeg_proxy_urls):
            streams_html += f"""
                <div class="stream">
                    <img src="{url}" alt="Video Stream {url}" />
                </div>"""

        # 这是HTML模板的主体
        html_template = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>分屏布局 (MJPEG)</title>
            <style>
                body, html {{
                    margin: 0; padding: 0; height: 100vh; width: 100vw;
                    overflow: hidden; background-color: #333;
                }}
                .container {{
                    display: grid; width: 100%; height: 100%; gap: 3px;
                }}
                .stream {{
                    background-color: #000; overflow: hidden; display: none;
                    justify-content: center; align-items: center;
                }}
                img {{
                    width: 100%; height: 100%; object-fit: contain;
                    color: white; text-align: center; /* 加载失败时的样式 */
                }}
                .controls {{
                    position: fixed; bottom: 10px; left: 50%;
                    transform: translateX(-50%); background-color: rgba(0, 0, 0, 0.5);
                    padding: 5px; border-radius: 5px; z-index: 100;
                }}
                .controls button {{
                    padding: 8px 16px; margin: 0 5px; font-size: 14px;
                    background-color: #007bff; color: #fff; border: none;
                    border-radius: 4px; cursor: pointer;
                }}
                .controls button:hover {{ background-color: #0056b3; }}
            </style>
        </head>
        <body>
            <div class="container">
                {streams_html}
            </div>
            <div class="controls">
                <button onclick="setLayout(1)">1分屏</button>
                <button onclick="setLayout(4)">4分屏</button>
                <button onclick="setLayout(6)">6分屏</button>
                <button onclick="setLayout(9)">9分屏</button>
            </div>
            <script>
                const container = document.querySelector('.container');
                const streams = document.querySelectorAll('.stream');
                function setLayout(layout) {{
                    streams.forEach((stream, index) => {{
                        stream.style.display = (index < layout) ? 'flex' : 'none';
                        stream.style.gridArea = 'auto';
                    }});
                    const layouts = {{
                        1: {{ cols: '1fr', rows: '1fr' }},
                        4: {{ cols: '1fr 1fr', rows: '1fr 1fr' }},
                        6: {{ cols: 'repeat(3, 1fr)', rows: 'repeat(2, 1fr)' }},
                        9: {{ cols: '1fr 1fr 1fr', rows: '1fr 1fr 1fr' }}
                    }};
                    const config = layouts[layout] || layouts[9];
                    container.style.gridTemplateColumns = config.cols;
                    container.style.gridTemplateRows = config.rows;
                }}
                setLayout(9);
            </script>
        </body>
        </html>
        """
        # 将生成的 img 标签部分填入模板
        return html_template.format(streams_html=streams_html)
    
    def closeEvent(self, event):
        """当主窗口关闭时，确保HTTP服务器和所有FFmpeg进程都关闭"""
        print("窗口关闭，正在清理资源...")

        # 首先清理所有 FFmpeg 进程
        cleanup_all_ffmpeg_processes()

        # 然后停止 HTTP 服务器
        if self.httpd:
            try:
                self.httpd.shutdown()
                self.httpd.server_close()
                print("HTTP 服务器已停止")
            except Exception as e:
                print(f"停止 HTTP 服务器时出错: {e}")

        # 等待 HTTP 线程结束
        if self.http_thread and self.http_thread.is_alive():
            self.http_thread.join(timeout=2)

        print("资源清理完成")
        event.accept()

if __name__ == "__main__":
    # 调试环境变量
    os.environ['QTWEBENGINE_REMOTE_DEBUGGING'] = '8090'

    app = QApplication(sys.argv)
    groupscreen = Groupscreen()
    groupscreen.show()

    print("应用已启动。如果需要调试，请在浏览器中访问 http://localhost:8090")
    
    sys.exit(app.exec_())