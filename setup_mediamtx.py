#!/usr/bin/env python3
"""
MediaMTX配置和启动脚本
确保MediaMTX正确配置用于投屏功能
"""

import os
import sys
import yaml
import subprocess
import time

def check_mediamtx_exists():
    """检查MediaMTX是否存在"""
    mediamtx_path = os.path.join(os.getcwd(), 'mediamtx_v1.8.4_linux_amd64', 'mediamtx')
    config_path = os.path.join(os.getcwd(), 'mediamtx_v1.8.4_linux_amd64', 'mediamtx.yml')
    
    if not os.path.exists(mediamtx_path):
        print(f"错误: MediaMTX可执行文件不存在: {mediamtx_path}")
        return False, None, None
    
    if not os.path.exists(config_path):
        print(f"错误: MediaMTX配置文件不存在: {config_path}")
        return False, None, None
    
    # 检查可执行权限
    if not os.access(mediamtx_path, os.X_OK):
        print(f"设置MediaMTX可执行权限: {mediamtx_path}")
        os.chmod(mediamtx_path, 0o755)
    
    return True, mediamtx_path, config_path

def optimize_mediamtx_config(config_path):
    """优化MediaMTX配置用于投屏"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 优化配置
        optimizations = {
            # 启用API用于监控
            'api': True,
            'apiAddress': ':9997',
            
            # 优化HLS设置
            'hls': True,
            'hlsAddress': ':8888',
            'hlsVariant': 'lowLatency',  # 使用低延迟HLS
            'hlsSegmentDuration': '1s',  # 1秒段长度
            'hlsPartDuration': '200ms',  # 200ms部分长度
            'hlsSegmentCount': 5,  # 保持5个段
            'hlsAlwaysRemux': True,  # 总是重新复用
            
            # 优化RTSP设置
            'rtsp': True,
            'rtspAddress': ':8554',
            'protocols': ['tcp'],  # 只使用TCP协议提高稳定性
            
            # 禁用不需要的服务
            'rtmp': False,
            'webrtc': False,
            'srt': False,
            'metrics': False,
            'pprof': False,
            'playback': False,
        }
        
        # 应用优化
        for key, value in optimizations.items():
            config[key] = value
        
        # 优化路径默认设置
        if 'pathDefaults' not in config:
            config['pathDefaults'] = {}
        
        path_optimizations = {
            'source': 'publisher',
            'sourceOnDemand': False,  # 不按需启动
            'maxReaders': 10,  # 最大10个读者
        }
        
        for key, value in path_optimizations.items():
            config['pathDefaults'][key] = value
        
        # 备份原配置
        backup_path = config_path + '.backup'
        if not os.path.exists(backup_path):
            with open(backup_path, 'w', encoding='utf-8') as f:
                with open(config_path, 'r', encoding='utf-8') as orig:
                    f.write(orig.read())
            print(f"原配置已备份到: {backup_path}")
        
        # 写入优化后的配置
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        print("MediaMTX配置已优化")
        return True
        
    except Exception as e:
        print(f"优化MediaMTX配置失败: {e}")
        return False

def test_mediamtx_startup(mediamtx_path, config_path):
    """测试MediaMTX启动"""
    try:
        print("测试MediaMTX启动...")
        
        # 启动MediaMTX
        process = subprocess.Popen(
            [mediamtx_path],
            cwd=os.path.dirname(mediamtx_path),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待启动
        time.sleep(3)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✓ MediaMTX启动成功")
            
            # 测试API端点
            try:
                import requests
                response = requests.get('http://localhost:9997/v3/config/global/get', timeout=5)
                if response.status_code == 200:
                    print("✓ MediaMTX API可访问")
                else:
                    print("✗ MediaMTX API不可访问")
            except Exception as e:
                print(f"✗ MediaMTX API测试失败: {e}")
            
            # 停止测试进程
            process.terminate()
            process.wait()
            print("✓ MediaMTX测试完成")
            return True
        else:
            # 获取错误信息
            stdout, stderr = process.communicate()
            print(f"✗ MediaMTX启动失败")
            print(f"stdout: {stdout}")
            print(f"stderr: {stderr}")
            return False
            
    except Exception as e:
        print(f"✗ MediaMTX启动测试失败: {e}")
        return False

def create_start_script():
    """创建MediaMTX启动脚本"""
    script_content = '''#!/bin/bash
# MediaMTX启动脚本

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
MEDIAMTX_DIR="$SCRIPT_DIR/mediamtx_v1.8.4_linux_amd64"
MEDIAMTX_BIN="$MEDIAMTX_DIR/mediamtx"

if [ ! -f "$MEDIAMTX_BIN" ]; then
    echo "错误: MediaMTX可执行文件不存在: $MEDIAMTX_BIN"
    exit 1
fi

echo "启动MediaMTX..."
cd "$MEDIAMTX_DIR"
exec "$MEDIAMTX_BIN"
'''
    
    script_path = 'start_mediamtx.sh'
    with open(script_path, 'w') as f:
        f.write(script_content)
    
    os.chmod(script_path, 0o755)
    print(f"✓ 启动脚本已创建: {script_path}")

def main():
    print("MediaMTX配置和测试工具")
    print("=" * 40)
    
    # 检查MediaMTX是否存在
    exists, mediamtx_path, config_path = check_mediamtx_exists()
    if not exists:
        print("请确保MediaMTX已正确安装")
        return 1
    
    print(f"✓ MediaMTX路径: {mediamtx_path}")
    print(f"✓ 配置文件路径: {config_path}")
    
    # 优化配置
    if optimize_mediamtx_config(config_path):
        print("✓ 配置优化完成")
    else:
        print("✗ 配置优化失败")
        return 1
    
    # 测试启动
    if test_mediamtx_startup(mediamtx_path, config_path):
        print("✓ MediaMTX测试通过")
    else:
        print("✗ MediaMTX测试失败")
        return 1
    
    # 创建启动脚本
    create_start_script()
    
    print("\n" + "=" * 40)
    print("MediaMTX配置完成！")
    print("现在可以使用改进的投屏功能了。")
    print("\n使用方法:")
    print("1. 运行 python test_screen_cast.py 进行测试")
    print("2. 或者直接运行投屏模块")
    print("3. 手动启动MediaMTX: ./start_mediamtx.sh")
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
