#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一的登录状态管理器
用于管理整个应用的登录状态，确保所有子模块都能正确获取登录信息
"""

import json
import os
import pickle
import requests
from datetime import datetime, timedelta
from PyQt5.QtWidgets import QMessageBox, QApplication
from PyQt5.QtCore import QObject, pyqtSignal


class LoginManager(QObject):
    """统一的登录状态管理器"""
    
    # 登录状态变化信号
    login_status_changed = pyqtSignal(bool)  # True: 已登录, False: 未登录
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            cls._instance = super(LoginManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化登录管理器"""
        if self._initialized:
            return
            
        super().__init__()
        self._initialized = True

        # 获取项目根目录路径
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        # 配置文件完整路径
        self.config_file = os.path.join(project_root, 'config.json')
        # 登录状态文件完整路径
        self.session_file = os.path.join(project_root, './session_data.pkl')
        
        # 当前登录状态
        self.is_logged_in = False
        self.teacher_info = {}
        self.session = None
        self.login_time = None
        self.session_timeout = timedelta(hours=1)  # 会话超时时间
        
        # 加载配置
        self.config = self.load_config()
        
        # 尝试恢复登录状态
        self.restore_session()
    
    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return {
                "server": {"url": "http://localhost:8080", "timeout": 5},
                "saved_login": {"user_id": "", "password": "", "remember": False}
            }
    
    def save_session(self):
        """保存登录会话到文件"""
        if not self.is_logged_in:
            return
            
        try:
            session_data = {
                'teacher_info': self.teacher_info,
                'login_time': self.login_time,
                'session_cookies': self.session.cookies.get_dict() if self.session else {},
                'server_url': self.config['server']['url']
            }
            
            with open(self.session_file, 'wb') as f:
                pickle.dump(session_data, f)
            print("登录会话已保存")
        except Exception as e:
            print(f"保存登录会话失败: {e}")
    
    def restore_session(self):
        """从文件恢复登录会话"""
        if not os.path.exists(self.session_file):
            return False
            
        try:
            with open(self.session_file, 'rb') as f:
                session_data = pickle.load(f)
            
            # 检查会话是否过期
            login_time = session_data.get('login_time')
            if login_time and datetime.now() - login_time > self.session_timeout:
                print("登录会话已过期")
                self.clear_session()
                return False
            
            # 恢复会话数据
            self.teacher_info = session_data.get('teacher_info', {})
            self.login_time = login_time
            
            # 重建session对象
            self.session = requests.Session()
            cookies = session_data.get('session_cookies', {})
            for name, value in cookies.items():
                self.session.cookies.set(name, value)
            
            # 验证会话是否仍然有效
            if self.validate_session():
                self.is_logged_in = True
                print(f"成功恢复登录会话: {self.teacher_info.get('teacher_name', '未知教师')}")
                self.login_status_changed.emit(True)
                return True
            else:
                print("登录会话验证失败")
                self.clear_session()
                return False
                
        except Exception as e:
            print(f"恢复登录会话失败: {e}")
            self.clear_session()
            return False
    
    def validate_session(self):
        """验证当前会话是否有效"""
        if not self.session:
            return False
            
        try:
            response = self.session.get(
                f"{self.config['server']['url']}/teacher/get_teacher_info",
                timeout=self.config['server']['timeout']
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get('status') == 'success'
            else:
                return False
                
        except Exception as e:
            print(f"会话验证失败: {e}")
            return False
    
    def login(self, user_id, password):
        """执行登录操作"""
        try:
            # 创建新的session
            self.session = requests.Session()
            
            # 执行登录
            login_data = {'user_id': user_id, 'password': password}
            response = self.session.post(
                f"{self.config['server']['url']}/login",
                data=login_data,
                timeout=self.config['server']['timeout']
            )
            
            if response.status_code == 200:
                # 检查是否重定向到教师页面
                if '/dashboard' in response.url or 'teacher' in response.text.lower():
                    # 获取教师详细信息
                    teacher_info_response = self.session.get(
                        f"{self.config['server']['url']}/teacher/get_teacher_info",
                        timeout=self.config['server']['timeout']
                    )
                    
                    teacher_name = '教师'  # 默认值
                    if teacher_info_response.status_code == 200:
                        teacher_data = teacher_info_response.json()
                        if teacher_data.get('status') == 'success':
                            teacher_info_data = teacher_data.get('teacher_info', {})
                            teacher_name = teacher_info_data.get('name', '教师')
                    
                    # 设置登录状态
                    self.teacher_info = {
                        'user_id': user_id,
                        'teacher_name': teacher_name,
                        'session': self.session,
                        'server_url': self.config['server']['url']
                    }
                    self.login_time = datetime.now()
                    self.is_logged_in = True
                    
                    # 保存会话
                    self.save_session()
                    
                    # 发送登录成功信号
                    self.login_status_changed.emit(True)
                    
                    print(f"登录成功: {teacher_name}")
                    return True, "登录成功"
                else:
                    return False, "用户ID或密码错误"
            else:
                return False, f"登录失败: HTTP {response.status_code}"
                
        except requests.exceptions.RequestException as e:
            return False, f"网络连接失败: {str(e)}"
        except Exception as e:
            return False, f"登录失败: {str(e)}"
    
    def logout(self):
        """退出登录"""
        self.clear_session()
        self.login_status_changed.emit(False)
        print("已退出登录")
    
    def clear_session(self):
        """清除登录会话"""
        self.is_logged_in = False
        self.teacher_info = {}
        self.session = None
        self.login_time = None
        
        # 删除会话文件
        if os.path.exists(self.session_file):
            try:
                os.remove(self.session_file)
            except Exception as e:
                print(f"删除会话文件失败: {e}")
    
    def get_teacher_info(self):
        """获取当前登录的教师信息"""
        if self.is_logged_in:
            return self.teacher_info.copy()
        else:
            return None
    
    def get_session(self):
        """获取当前登录的session"""
        if self.is_logged_in:
            return self.session
        else:
            return None
    
    def require_login(self, parent_widget=None):
        """要求登录，如果未登录则显示登录界面"""
        if self.is_logged_in:
            return True
        
        # 显示登录提示
        if parent_widget:
            reply = QMessageBox.question(
                parent_widget, 
                '需要登录', 
                '此功能需要先登录。是否现在登录？',
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )
            
            if reply == QMessageBox.Yes:
                # 启动登录窗口
                return self.show_login_window()
            else:
                return False
        else:
            print("需要登录才能使用此功能")
            return False
    
    def show_login_window(self):
        """显示登录窗口"""
        try:
            from login_window import LoginWindow
            
            login_window = LoginWindow()
            
            def on_login_success(teacher_info):
                # 更新登录管理器状态
                self.teacher_info = teacher_info
                self.session = teacher_info.get('session')
                self.login_time = datetime.now()
                self.is_logged_in = True
                self.save_session()
                self.login_status_changed.emit(True)
            
            login_window.login_success.connect(on_login_success)
            result = login_window.exec_()
            
            return result == login_window.Accepted and self.is_logged_in
            
        except Exception as e:
            print(f"显示登录窗口失败: {e}")
            return False


# 全局登录管理器实例
login_manager = LoginManager()


def get_login_manager():
    """获取全局登录管理器实例"""
    return login_manager


def require_login(func):
    """装饰器：要求登录才能执行的函数"""
    def wrapper(*args, **kwargs):
        if not login_manager.is_logged_in:
            print(f"函数 {func.__name__} 需要登录才能执行")
            return None
        return func(*args, **kwargs)
    return wrapper
