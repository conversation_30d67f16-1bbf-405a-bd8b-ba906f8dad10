"""
Mastaji Classroom Toolkit - Main Application Interface (Refactored).

This file defines the main window for the Mastaji classroom toolkit, featuring
a streamlined, efficient, and cohesive user interface.

Key Features:
- A self-contained, floating toolbar that slides in from the right edge.
- The hide/show toggle button is integrated directly into the toolbar panel.
- A central launcher for modules like Whiteboard, Screen Broadcasting, etc.
- Simplified one-way buttons that only launch modules, not close them.
- Integration with a login system and a hardware-based registration system.
- Lazy loading of feature modules to conserve resources.
"""

import pickle
import sys
import hashlib
from PyQt5.QtWidgets import *
from PyQt5.QtGui import *
from PyQt5.QtCore import *

from ui.sys_css import MyCss
from modules.screen_broadcasting import BroadcastApp
from modules.screen_dlan import ScreenDLAN
from modules.file_sharing import FileSharingApp
from modules.whiteboard import Whiteboard
from modules.select_people import RandomStudentPicker
from modules.open_platform import OpenPlatformApp
from modules.group_screen import Groupscreen
from modules.danma_ku import <PERSON><PERSON><PERSON><PERSON>eacherA<PERSON>
from modules.control_pc import ControlPCAPP
# from AI_Attendance import AttendanceSystem
from core.registration_app import generate_hardware_fingerprint
from core.login_manager import get_login_manager

# Constants for menu dimensions
MENU_WIDTH_EXPANDED = 200
MENU_WIDTH_COLLAPSED = 40
MENU_HEIGHT = 600

class MainWindow(QMainWindow):
    def __init__(self, teacher_info=None, current_course_id=None):
        super().__init__()

        self.login_manager = get_login_manager()

        self.teacher_info = teacher_info or self.login_manager.get_teacher_info()
        self.current_course_id = current_course_id

        if not self.teacher_info:
            self._require_login_and_restart()
            return

        # Initialize module instances to None for lazy loading
        self._init_module_instances()

        # Load registration status
        self._load_registration_state()
        self._check_trial_or_registration()

        # Get screen dimensions
        screen = QApplication.primaryScreen()
        self.screen_rect = screen.availableGeometry()
        
        # Set up main window properties
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)
        # Initial geometry is off-screen to prevent flickering
        self.setGeometry(self.screen_rect.width(), 0, 0, 0)

        # Create the main menu panel
        self._setup_menu_panel()

        # Set up the animation for collapsing/expanding the menu
        self._setup_animation()
        
        # Set initial state
        self.is_menu_expanded = False
        self._toggle_menu_visibility() # Start in expanded state

    def _init_module_instances(self):
        """Initializes all module attributes to None."""
        self.attendance_app = None
        self.whiteboard_app = None
        self.screen_broadcasting_app = None
        self.screen_dlan_app = None
        self.file_sharing_app = None
        self.random_student_picker_app = None
        self.open_platform_app = None
        self.group_screen_app = None
        self.danmaku_app = None
        self.control_pc_app = None

    def _setup_menu_panel(self):
        """Creates and configures the main menu panel and its layout."""
        self.menu_panel = QWidget()
        self.menu_panel.setWindowFlags(Qt.FramelessWindowHint | Qt.Tool | Qt.WindowStaysOnTopHint)
        self.menu_panel.setAttribute(Qt.WA_TranslucentBackground)
        
        initial_y = (self.screen_rect.height() - MENU_HEIGHT) // 2
        self.menu_panel.setGeometry(self.screen_rect.width(), initial_y, MENU_WIDTH_EXPANDED, MENU_HEIGHT)
        self.menu_panel.setStyleSheet(MyCss.mainBgcolora)

        # Main horizontal layout: [Toggle Button | Content]
        panel_layout = QHBoxLayout(self.menu_panel)
        panel_layout.setContentsMargins(0, 0, 0, 0)
        panel_layout.setSpacing(0)

        # 1. Toggle Button
        self.toggle_button = QPushButton("→")
        self.toggle_button.setFixedSize(MENU_WIDTH_COLLAPSED, MENU_HEIGHT)
        #self.toggle_button.setStyleSheet(MyCss.toggleButCss)
        self.toggle_button.clicked.connect(self._toggle_menu_visibility)
        panel_layout.addWidget(self.toggle_button)

        # 2. Content Widget (holds all tools)
        self.content_widget = QWidget()
        panel_layout.addWidget(self.content_widget)

        # Vertical layout for the content widget
        content_layout = QVBoxLayout(self.content_widget)
        content_layout.setSpacing(2)
        content_layout.setContentsMargins(10, 5, 10, 5)

        # Add components to the content layout
        self._add_timer_to_layout(content_layout)
        self._add_buttons_to_layout(content_layout)
        self._add_logo_to_layout(content_layout)
        
        self.menu_panel.show()

    def _add_timer_to_layout(self, layout):
        """Adds the countdown timer display."""
        self.timer_label = QLabel("60:00")
        self.timer_label.setAlignment(Qt.AlignCenter)
        self.timer_label.setStyleSheet("font-size: 18px; font-weight: bold; color: white;")
        self.timer_label.setFixedHeight(100)
        layout.addWidget(self.timer_label)

        # Setup and start the timer
        self.remaining_time_seconds = 3600
        self.countdown_timer = QTimer(self)
        self.countdown_timer.timeout.connect(self._update_countdown_timer)
        self.countdown_timer.start(1000)

    def _add_buttons_to_layout(self, layout):
        """Adds the grid of feature buttons."""
        button_grid_widget = QWidget()
        button_layout = QGridLayout(button_grid_widget)
        button_layout.setSpacing(2)
        button_layout.setContentsMargins(0, 0, 0, 0)

        buttons_to_create = [
            ("批注", self._open_whiteboard, "打开电子批注"),
            ("投屏", self._open_screen_dlan, "将屏幕投放到其他设备"),
            ("广播", self._open_screen_broadcast, "广播屏幕内容"),
            ("互动", self._open_group_screen, "与分组屏幕互动"),
            ("弹幕", self._open_danmaku, "分组端发送弹幕"),
            ("选人", self._open_random_picker, "随机选人"),
            ("控制", self._open_control_pc, "一键开关分组屏"),
            ("分享", self._open_file_sharing, "扫码下载"),
            ("平台", self._open_open_platform, "可视化资源平台"),
            ("注册", self._show_registration_dialog, "注册系统"),
            ("退出", self._prompt_to_close_application, "退出系统"),
        ]

        for i, (text, callback, tooltip) in enumerate(buttons_to_create):
            button = QPushButton(text)
            button.setFixedSize(64, 50)
            button.setStyleSheet(MyCss.butBCss)
            button.setToolTip(tooltip)
            button.clicked.connect(self._create_module_launcher(text, callback))
            button_layout.addWidget(button, i // 2, i % 2)
            
        layout.addWidget(button_grid_widget)

    def _add_logo_to_layout(self, layout):
        """Adds the logo to the bottom of the menu."""
        logo_label = QLabel()
        logo_label.setAlignment(Qt.AlignCenter)
        logo_pixmap = QPixmap("./assets/images/logo.png")
        logo_label.setPixmap(logo_pixmap.scaled(100, 100, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        logo_label.setFixedHeight(100)
        layout.addWidget(logo_label)
    
    def _setup_animation(self):
        """Configures the property animation for the menu panel."""
        self.animation = QPropertyAnimation(self.menu_panel, b"geometry")
        self.animation.setDuration(300)
        self.animation.setEasingCurve(QEasingCurve.OutCubic)
        self.animation.finished.connect(self._on_animation_finished)

    def _toggle_menu_visibility(self):
        """Handles the logic for expanding and collapsing the menu."""
        start_rect = self.menu_panel.geometry()
        screen_width = self.screen_rect.width()
        
        if self.is_menu_expanded:
            # Collapse the menu
            end_rect = QRect(screen_width - MENU_WIDTH_COLLAPSED, start_rect.y(), MENU_WIDTH_COLLAPSED, MENU_HEIGHT)
            self.toggle_button.setText("←")
        else:
            # Expand the menu
            self.content_widget.show() # Show content before animation starts
            end_rect = QRect(screen_width - MENU_WIDTH_EXPANDED, start_rect.y(), MENU_WIDTH_EXPANDED, MENU_HEIGHT)
            self.toggle_button.setText("→")

        self.animation.setStartValue(start_rect)
        self.animation.setEndValue(end_rect)
        self.animation.start()
        
        self.is_menu_expanded = not self.is_menu_expanded

    def _on_animation_finished(self):
        """Called after the animation completes to finalize the state."""
        if not self.is_menu_expanded:
            self.content_widget.hide() # Hide content after collapsing
        self.menu_panel.raise_()

    def _create_module_launcher(self, module_name, launch_function):
        """
        Creates a simplified click handler that launches a module after checking registration.
        This version does not handle closing modules.
        """
        def launcher():
            # Allow 'Register' and 'Exit' buttons to always work
            if module_name in ["注册", "退出"]:
                launch_function()
                return

            # Check for trial period
            if self.is_in_trial and self.trial_remaining_seconds > 0:
                launch_function()
                return
            
            # Check for registration
            if self.is_registered:
                launch_function()
                return

            # If neither, show a warning
            QMessageBox.warning(self, "功能受限", "此功能需要注册。请点击'注册'按钮或在试用期内使用。")
        
        return launcher

    # --- Registration and State Management ---

    def _load_registration_state(self):
        """Loads registration and trial status from a pickle file."""
        try:
            with open('./registration.pkl', 'rb') as f:
                data = pickle.load(f)
                self.is_registered = data.get('registered', False)
                self.is_in_trial = data.get('trial_period', False)
                self.trial_remaining_seconds = data.get('trial_remaining_seconds', 0)
                self.hardware_fingerprint = data.get('hardware_fingerprint', None)
        except (FileNotFoundError, EOFError):
            self.is_registered = False
            self.is_in_trial = False
            self.trial_remaining_seconds = 0
            self.hardware_fingerprint = None

    def _save_registration_state(self):
        """Saves the current registration and trial status to a pickle file."""
        data = {
            'registered': self.is_registered,
            'trial_period': self.is_in_trial,
            'trial_remaining_seconds': self.trial_remaining_seconds,
            'hardware_fingerprint': self.hardware_fingerprint
        }
        with open('./registration.pkl', 'wb') as f:
            pickle.dump(data, f)

    def _check_trial_or_registration(self):
        """Checks status on startup and informs the user."""
        if self.is_registered:
             QMessageBox.information(self, "欢迎", "系统已注册，所有功能可用。")
             return

        if not self.is_in_trial and self.trial_remaining_seconds <= 0:
            self.is_in_trial = True
            self.trial_remaining_seconds = 86400  # 1 day trial
            self._save_registration_state()
            QMessageBox.information(self, "试用期开始", f"您已开始1天试用期。")
        elif self.is_in_trial and self.trial_remaining_seconds > 0:
            QMessageBox.information(self, "试用中", f"试用期剩余时间：{self._format_seconds_to_time(self.trial_remaining_seconds)}")
        elif self.is_in_trial and self.trial_remaining_seconds <= 0:
            self.is_in_trial = False
            self._save_registration_state()
            QMessageBox.warning(self, "试用期结束", "您的试用期已结束，请注册以继续使用。")

    def _show_registration_dialog(self):
        """Handles the software registration process."""
        if self.is_registered:
            QMessageBox.information(self, "已注册", "本系统已经成功注册。")
            return

        self.hardware_fingerprint = generate_hardware_fingerprint()
        self._save_registration_state()

        QMessageBox.information(self, "硬件指纹", f"请将以下硬件指纹码提供给管理员以获取注册码：\n\n{self.hardware_fingerprint}")

        code, ok = QInputDialog.getText(self, "输入注册码", "请输入您的注册码:")
        if ok and code:
            expected_code = self._generate_registration_code(self.hardware_fingerprint)
            if code.strip() == expected_code:
                self.is_registered = True
                self.is_in_trial = False
                self._save_registration_state()
                QMessageBox.information(self, "成功", "注册成功！所有功能已解锁。")
            else:
                QMessageBox.warning(self, "失败", "注册码无效，请重新尝试。")

    def _generate_registration_code(self, hardware_fingerprint):
        """Generates the expected registration code from a hardware fingerprint."""
        hash_obj = hashlib.md5(hardware_fingerprint.encode())
        hex_digest = hash_obj.hexdigest()
        # Format as XXXXX-XXXXX-XXXXX-XXXXX-XXXXX
        return "-".join(hex_digest[i:i+5].upper() for i in range(0, 25, 5))


    # --- Launch Methods for Modules (Lazy Loading) ---

    def _open_whiteboard(self):
        if self.whiteboard_app is None:
            self.whiteboard_app = Whiteboard()
        self.whiteboard_app.showFullScreen()

    def _open_screen_dlan(self):
        if self.screen_dlan_app is None:
            self.screen_dlan_app = ScreenDLAN()
        self.screen_dlan_app.show()

    def _open_screen_broadcast(self):
        if self.screen_broadcasting_app is None:
            self.screen_broadcasting_app = BroadcastApp()
        self.screen_broadcasting_app.show()

    def _open_group_screen(self):
        if self.group_screen_app is None:
            self.group_screen_app = Groupscreen()
        self.group_screen_app.show()

    def _open_danmaku(self):
        if self.danmaku_app is None:
            self.danmaku_app = DanmakuTeacherApp()
        self.danmaku_app.showFullScreen()

    def _open_random_picker(self):
        if self.random_student_picker_app is None:
            self.random_student_picker_app = RandomStudentPicker(
                teacher_info=self.teacher_info, 
                current_course_id=self.current_course_id
            )
        self.random_student_picker_app.show()

    def _open_control_pc(self):
        if self.control_pc_app is None:
            self.control_pc_app = ControlPCAPP()
        self.control_pc_app.show()

    def _open_file_sharing(self):
        if self.file_sharing_app is None:
            self.file_sharing_app = FileSharingApp()
        self.file_sharing_app.show()

    def _open_open_platform(self):
        if self.open_platform_app is None:
            self.open_platform_app = OpenPlatformApp()
        self.open_platform_app.show()

    # --- Utility and System Methods ---

    def _update_countdown_timer(self):
        """Updates the timer label text each second."""
        if self.remaining_time_seconds > 0:
            self.remaining_time_seconds -= 1
            if self.is_in_trial:
                self.trial_remaining_seconds -= 1 # Also decrement trial time
            
            minutes = self.remaining_time_seconds // 60
            seconds = self.remaining_time_seconds % 60
            self.timer_label.setText(f"{minutes:02d}:{seconds:02d}")
        else:
            self.countdown_timer.stop()
            self.timer_label.setText("时间到")
            if self.is_in_trial: self._save_registration_state()

    def _format_seconds_to_time(self, seconds):
        """Formats seconds into a human-readable day/hour/minute string."""
        days, rem = divmod(seconds, 86400)
        hours, rem = divmod(rem, 3600)
        minutes, _ = divmod(rem, 60)
        return f"{int(days)}天 {int(hours)}小时 {int(minutes)}分钟"

    def _require_login_and_restart(self):
        """Forces the user to log in or exits the application."""
        QMessageBox.warning(self, '需要登录', '请先通过主程序登录后再使用课堂工具！')
        if self.login_manager.require_login(self):
            self.teacher_info = self.login_manager.get_teacher_info()
            print(f"登录成功: {self.teacher_info.get('teacher_name', '未知教师')}")
        else:
            # Schedule the close event to allow the message box to close first
            QTimer.singleShot(0, self.close)
            
    def _prompt_to_close_application(self):
        """Shows a confirmation dialog before quitting."""
        reply = QMessageBox.question(self, "退出系统", "您确定要退出吗？", 
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            QApplication.instance().quit()

    def closeEvent(self, event):
        """Ensures the registration state is saved on close."""
        self._save_registration_state()
        super().closeEvent(event)


if __name__ == '__main__':
    app = QApplication(sys.argv)
    # For testing, you can pass mock data
    # mock_teacher_info = {'teacher_name': 'Test Teacher', 'teacher_id': '123'}
    # main_win = MainWindow(teacher_info=mock_teacher_info, current_course_id='C101')
    sys.exit(app.exec_())