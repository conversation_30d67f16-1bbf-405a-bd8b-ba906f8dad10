#!/bin/bash
# 小马助教系统启动脚本

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 切换到项目目录
cd "$SCRIPT_DIR"

echo "启动小马助教系统..."
echo "项目目录: $SCRIPT_DIR"

# 检查Python环境
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
else
    echo "错误: 未找到Python解释器"
    exit 1
fi

echo "使用Python解释器: $PYTHON_CMD"

# 检查虚拟环境
if [ -d "venv" ]; then
    echo "激活虚拟环境..."
    source venv/bin/activate
fi

# 启动主程序
echo "启动主程序..."
$PYTHON_CMD main.py

echo "程序已退出"
